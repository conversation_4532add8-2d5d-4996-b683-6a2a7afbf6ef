import 'package:get/get.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

class ProfileController extends GetxController {
  static ProfileController get instance => Get.find();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;

  // Observable user data
  final Rx<Map<String, dynamic>?> _userData = Rx<Map<String, dynamic>?>(null);
  final RxBool _isLoading = false.obs;
  final RxString _profileImageUrl = ''.obs;
  final RxString _coverImageUrl = ''.obs;
  final RxString _username = ''.obs;
  final RxString _displayName = ''.obs;
  final RxString _bio = ''.obs;
  final RxString _selectedCategory = 'News'.obs;

  // Getters for reactive data
  Map<String, dynamic>? get userData => _userData.value;
  bool get isLoading => _isLoading.value;
  String get profileImageUrl => _profileImageUrl.value;
  String get coverImageUrl => _coverImageUrl.value;
  String get username => _username.value;
  String get displayName => _displayName.value;
  String get bio => _bio.value;
  String get selectedCategory => _selectedCategory.value;

  // Current user ID
  String? get currentUserId => _auth.currentUser?.uid;

  @override
  void onInit() {
    super.onInit();
    // Load current user data if logged in
    if (currentUserId != null) {
      loadUserData(currentUserId!);
    }
  }

  /// Load user data for a specific user ID
  Future<void> loadUserData(String userId) async {
    try {
      _isLoading.value = true;

      final doc = await _firestore.collection('users').doc(userId).get();

      if (doc.exists) {
        final data = doc.data()!;
        data['id'] = doc.id;

        _userData.value = data;
        _updateObservables(data);

        debugPrint('ProfileController: Loaded user data for $userId');
      } else {
        _userData.value = null;
        _clearObservables();
      }
    } catch (e) {
      debugPrint('ProfileController: Error loading user data: $e');
      _userData.value = null;
      _clearObservables();
    } finally {
      _isLoading.value = false;
    }
  }

  /// Update profile image URL and notify all listeners
  Future<void> updateProfileImage(String userId, String newImageUrl) async {
    try {
      // Update in Firestore first
      await _firestore.collection('users').doc(userId).update({
        'profileImageUrl': newImageUrl,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Update local state immediately for instant UI update (only if it's current user)
      if (userId == currentUserId && _userData.value != null) {
        final newData = Map<String, dynamic>.from(_userData.value!);
        newData['profileImageUrl'] = newImageUrl;
        _userData.value = newData;
        _profileImageUrl.value = newImageUrl;
      }

      // If this is the current user, also update Firebase Auth
      if (userId == currentUserId) {
        await _auth.currentUser?.updatePhotoURL(newImageUrl);
      }

      debugPrint(
        'ProfileController: Updated profile image for $userId to $newImageUrl',
      );
    } catch (e) {
      debugPrint('ProfileController: Error updating profile image: $e');
      rethrow;
    }
  }

  /// Update profile data and notify all listeners
  Future<void> updateProfileData(
    String userId,
    Map<String, dynamic> data,
  ) async {
    try {
      // Update in Firestore
      final updateData = {...data, 'updatedAt': FieldValue.serverTimestamp()};

      await _firestore.collection('users').doc(userId).update(updateData);

      // Update local state immediately for instant UI update (only if it's current user)
      if (userId == currentUserId && _userData.value != null) {
        final newData = Map<String, dynamic>.from(_userData.value!);
        newData.addAll(data);
        _userData.value = newData;
        _updateObservables(newData);
      }

      // If this is the current user and display name changed, update Firebase Auth
      if (userId == currentUserId && data.containsKey('name')) {
        await _auth.currentUser?.updateDisplayName(data['name']);
      }

      debugPrint(
        'ProfileController: Updated profile data for $userId with keys: ${data.keys}',
      );
    } catch (e) {
      debugPrint('ProfileController: Error updating profile data: $e');
      rethrow;
    }
  }

  /// Force refresh user data from Firestore
  Future<void> refreshUserData(String userId) async {
    await loadUserData(userId);
  }

  /// Get user data for any user ID (with caching)
  Future<Map<String, dynamic>?> getUserData(String userId) async {
    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        final data = doc.data()!;
        data['id'] = doc.id;
        return data;
      }
      return null;
    } catch (e) {
      debugPrint('ProfileController: Error getting user data for $userId: $e');
      return null;
    }
  }

  /// Get profile image URL for any user
  Future<String?> getProfileImageUrl(String userId) async {
    final userData = await getUserData(userId);
    return userData?['profileImageUrl'];
  }

  /// Get display name for any user
  Future<String> getDisplayName(String userId) async {
    final userData = await getUserData(userId);
    return userData?['name'] ?? userData?['username'] ?? 'Unknown User';
  }

  /// Update observables from user data
  void _updateObservables(Map<String, dynamic> data) {
    _profileImageUrl.value = data['profileImageUrl'] ?? '';
    _coverImageUrl.value = data['coverImageUrl'] ?? '';
    _username.value = data['username'] ?? '';
    _displayName.value = data['name'] ?? '';
    _bio.value = data['bio'] ?? '';
    _selectedCategory.value = data['selectedCategory'] ?? 'News';
  }

  /// Clear all observables
  void _clearObservables() {
    _profileImageUrl.value = '';
    _coverImageUrl.value = '';
    _username.value = '';
    _displayName.value = '';
    _bio.value = '';
    _selectedCategory.value = 'News';
  }

  /// Listen to real-time updates for a specific user
  void listenToUserUpdates(String userId) {
    _firestore.collection('users').doc(userId).snapshots().listen((doc) {
      if (doc.exists) {
        final data = doc.data()!;
        data['id'] = doc.id;

        _userData.value = data;
        _updateObservables(data);

        debugPrint('ProfileController: Real-time update received for $userId');
      }
    });
  }

  /// Clear user data (for logout)
  void clearUserData() {
    _userData.value = null;
    _clearObservables();
    _isLoading.value = false;
  }
}
