import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:money_mouthy_two/services/post_service.dart';
import 'package:money_mouthy_two/services/user_service.dart';
import 'package:money_mouthy_two/services/profile_update_service.dart';
import 'package:money_mouthy_two/services/video_thumbnail_service.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:image_network/image_network.dart';

class TopPaidPostContainer extends StatefulWidget {
  final String category;
  final Post? topPost;
  final VoidCallback? onTap;

  const TopPaidPostContainer({
    super.key,
    required this.category,
    this.topPost,
    this.onTap,
  });

  @override
  State<TopPaidPostContainer> createState() => _TopPaidPostContainerState();
}

class _TopPaidPostContainerState extends State<TopPaidPostContainer>
    with SingleTickerProviderStateMixin {
  final UserService _userService = UserService();
  final ProfileUpdateService _profileUpdateService = ProfileUpdateService();
  String? _userProfileImage;
  String _userDisplayName = '';
  bool _isLoadingUserData = true;
  late AnimationController _animationController;
  late Animation<double> _pulseAnimation;
  StreamSubscription<Map<String, dynamic>>? _profileUpdateSubscription;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.02).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
    _loadUserData();

    // Listen to profile updates for real-time changes
    _profileUpdateSubscription = _profileUpdateService.profileDataUpdateStream
        .listen((updateData) {
          final userId = updateData['userId'] as String?;
          if (mounted && userId == widget.topPost?.authorId) {
            // Refresh user data when the post author's profile is updated
            _loadUserData();
          }
        });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _profileUpdateSubscription?.cancel();
    super.dispose();
  }

  @override
  void didUpdateWidget(TopPaidPostContainer oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.topPost?.authorId != widget.topPost?.authorId) {
      _loadUserData();
    }
  }

  Future<void> _loadUserData() async {
    if (widget.topPost == null) {
      setState(() => _isLoadingUserData = false);
      return;
    }

    try {
      final userData = await _userService.getUserData(widget.topPost!.authorId);
      if (mounted) {
        setState(() {
          _userProfileImage =
              userData?['profileImageUrl'] ?? userData?['photoUrl'];
          _userDisplayName =
              userData?['name'] ??
              userData?['username'] ??
              widget.topPost!.author;
          _isLoadingUserData = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');
      if (mounted) {
        setState(() {
          _userDisplayName = widget.topPost!.author;
          _isLoadingUserData = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.topPost == null) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 6,
          ), // Reduced margins
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color.fromARGB(
                  255,
                  255,
                  255,
                  255,
                ).withValues(alpha: 0.1), // Gold tint
                Colors.white,
                const Color.fromARGB(
                  255,
                  255,
                  255,
                  255,
                ).withValues(alpha: 0.05),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: const Color.fromARGB(
                255,
                37,
                35,
                20,
              ).withValues(alpha: 0.3),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: const Color.fromARGB(
                  255,
                  255,
                  255,
                  255,
                ).withValues(alpha: 0.2),
                blurRadius: 16,
                offset: const Offset(0, 3),
              ),
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.08),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: widget.onTap,
              borderRadius: BorderRadius.circular(16),
              child: Padding(
                padding: const EdgeInsets.all(12), // Reduced padding
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Top Paid Post Badge
                    const SizedBox(height: 6), // Reduced spacing
                    // Post content section
                    _buildPostContentSection(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPostContentSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // User info and post details
        Row(
          children: [
            _isLoadingUserData
                ? Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    shape: BoxShape.circle,
                  ),
                  child: const Center(
                    child: SizedBox(
                      width: 12,
                      height: 12,
                      child: CircularProgressIndicator(strokeWidth: 1.5),
                    ),
                  ),
                )
                : _userProfileImage != null
                ? CircleAvatar(
                  radius: 16,
                  backgroundColor: Colors.grey[200],
                  child: ClipOval(
                    child:
                        kIsWeb
                            ? ImageNetwork(
                              image: _userProfileImage!,
                              height: 32,
                              width: 32,
                              duration: 500,
                              curve: Curves.easeIn,
                              onPointer: true,
                              debugPrint: false,
                              backgroundColor: Colors.white,
                              fitAndroidIos: BoxFit.cover,
                              fitWeb: BoxFitWeb.cover,
                              borderRadius: BorderRadius.circular(70),
                              onLoading: const CircularProgressIndicator(
                                color: Colors.indigoAccent,
                                strokeWidth: 0.1,
                              ),
                              onError: Text(
                                _userDisplayName.isNotEmpty
                                    ? _userDisplayName[0].toUpperCase()
                                    : 'A',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                ),
                              ),
                            )
                            : Image.network(
                              _userProfileImage!,
                              height: 32,
                              width: 32,
                              fit: BoxFit.cover,
                              errorBuilder: (context, error, stackTrace) {
                                return Text(
                                  _userDisplayName.isNotEmpty
                                      ? _userDisplayName[0].toUpperCase()
                                      : 'A',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.bold,
                                    fontSize: 12,
                                  ),
                                );
                              },
                            ),
                  ),
                )
                : CircleAvatar(
                  radius: 16,
                  backgroundColor: Colors.grey[200],
                  child: Text(
                    _userDisplayName.isNotEmpty
                        ? _userDisplayName[0].toUpperCase()
                        : 'A',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
            const SizedBox(width: 6), // Reduced spacing
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFFFD700), Color(0xFFFFA500)],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: const Color(0xFFFFD700).withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.star, color: Colors.white, size: 16),
                  const SizedBox(width: 4),
                  const Text(
                    'TOP PAID POST',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.5,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _isLoadingUserData ? 'Loading...' : _userDisplayName,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  Text(
                    widget.topPost!.timeAgo,
                    style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: const Color(0xFF5159FF).withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(width: 4),
                  Text(
                    widget.topPost!.formattedPrice,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 8), // Reduced spacing
        // Post title
        // Text(
        //   widget.topPost!.content.split('\n').first,
        //   style: const TextStyle(
        //     fontSize: 16,
        //     fontWeight: FontWeight.w600,
        //     color: Colors.black87,
        //     height: 1.3,
        //   ),
        // ),

        // const SizedBox(height: 6), // Reduced spacing
        // Post content
        Text(
          widget.topPost!.content.length > 300
              ? '${widget.topPost!.content.substring(0, 300)}...'
              : widget.topPost!.content,
          style: TextStyle(fontSize: 14, color: Colors.grey[700], height: 1.4),
        ),

        // Media content
        if (_hasMediaContent()) ...[
          const SizedBox(height: 8), // Reduced spacing
          _buildMediaContent(),
        ],
        const SizedBox(height: 8), // Reduced spacing
        // // Action buttons
        // Row(
        //   children: [
        //     _buildActionButton(Icons.link, ''),
        //     const SizedBox(width: 16),
        //     _buildActionButton(Icons.repeat, widget.topPost!.likes.toString()),
        //     const SizedBox(width: 16),
        //     _buildActionButton(Icons.share, ''),
        //     const Spacer(),
        //     const Icon(Icons.more_horiz, color: Colors.grey),
        //   ],
        // ),
      ],
    );
  }

  Widget _buildVideoThumbnail(String videoUrl) {
    if (kIsWeb) {
      // For web, show a placeholder with video icon
      return Container(
        width: double.infinity,
        height: 120,
        color: Colors.black87,
        child: const Center(
          child: Icon(Icons.videocam, color: Colors.white, size: 24),
        ),
      );
    }

    // For mobile, use video thumbnail service
    return FutureBuilder<String?>(
      future: VideoThumbnailService.generateThumbnailFromUrl(videoUrl),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Container(
            width: double.infinity,
            height: 120,
            color: Colors.black87,
            child: const Center(
              child: SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: Colors.white,
                ),
              ),
            ),
          );
        }

        if (snapshot.hasData && snapshot.data != null) {
          return Image.file(
            File(snapshot.data!),
            width: double.infinity,
            height: 120,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return _buildVideoPlaceholder();
            },
          );
        }

        return _buildVideoPlaceholder();
      },
    );
  }

  Widget _buildVideoPlaceholder() {
    return Container(
      width: double.infinity,
      height: 120,
      color: Colors.black87,
      child: const Center(
        child: Icon(Icons.videocam, color: Colors.white, size: 24),
      ),
    );
  }

  bool _hasMediaContent() {
    if (widget.topPost == null) return false;
    return widget.topPost!.imageUrls.isNotEmpty ||
        widget.topPost!.videoUrls.isNotEmpty ||
        (widget.topPost!.linkUrl != null &&
            widget.topPost!.linkUrl!.isNotEmpty);
  }

  Widget _buildMediaContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Images in horizontal layout like reference design
        if (widget.topPost!.imageUrls.isNotEmpty) _buildImageGallery(),

        // Videos
        if (widget.topPost!.videoUrls.isNotEmpty) ...[
          if (widget.topPost!.imageUrls.isNotEmpty) const SizedBox(height: 6),
          _buildVideoSection(),
        ],

        // URL Link
        if (widget.topPost!.linkUrl != null &&
            widget.topPost!.linkUrl!.isNotEmpty) ...[
          if (widget.topPost!.imageUrls.isNotEmpty ||
              widget.topPost!.videoUrls.isNotEmpty)
            const SizedBox(height: 6),
          _buildLinkPreview(),
        ],
      ],
    );
  }

  Widget _buildImageGallery() {
    final images = widget.topPost!.imageUrls;
    if (images.isEmpty) return const SizedBox.shrink();

    // Single image - use full width with reduced height for space efficiency
    if (images.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: SizedBox(
          width: double.infinity,
          height: 120, // Reduced height to save space
          child:
              kIsWeb
                  ? ImageNetwork(
                    image: images[0],
                    height: 120,
                    width: double.infinity,
                    duration: 500,
                    curve: Curves.easeIn,
                    onPointer: true,
                    debugPrint: false,
                    backgroundColor: Colors.grey[100]!,
                    fitAndroidIos: BoxFit.cover,
                    fitWeb: BoxFitWeb.cover,
                    onLoading: const CircularProgressIndicator(
                      color: Colors.indigoAccent,
                      strokeWidth: 1.0,
                    ),
                    onError: Container(
                      height: 120,
                      color: Colors.grey[300],
                      child: const Icon(
                        Icons.image_not_supported,
                        color: Colors.grey,
                      ),
                    ),
                  )
                  : Image.network(
                    images[0],
                    height: 120,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 120,
                        color: Colors.grey[300],
                        child: const Icon(
                          Icons.image_not_supported,
                          color: Colors.grey,
                        ),
                      );
                    },
                  ),
        ),
      );
    }

    // Multiple images - show in horizontal scroll for better space usage
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: images.length,
        itemBuilder: (context, index) {
          return Container(
            width: 120,
            margin: EdgeInsets.only(right: index < images.length - 1 ? 6 : 0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child:
                  kIsWeb
                      ? ImageNetwork(
                        image: images[index],
                        height: 120,
                        width: 120,
                        duration: 500,
                        curve: Curves.easeIn,
                        onPointer: true,
                        debugPrint: false,
                        backgroundColor: Colors.grey[100]!,
                        fitAndroidIos: BoxFit.cover,
                        fitWeb: BoxFitWeb.cover,
                        onLoading: const CircularProgressIndicator(
                          color: Colors.indigoAccent,
                          strokeWidth: 1.0,
                        ),
                        onError: Container(
                          height: 120,
                          color: Colors.grey[300],
                          child: const Icon(
                            Icons.image_not_supported,
                            color: Colors.grey,
                            size: 20,
                          ),
                        ),
                      )
                      : Image.network(
                        images[index],
                        height: 120,
                        width: 120,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            height: 120,
                            color: Colors.grey[300],
                            child: const Icon(
                              Icons.image_not_supported,
                              color: Colors.grey,
                              size: 20,
                            ),
                          );
                        },
                      ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildVideoSection() {
    final videos = widget.topPost!.videoUrls;
    if (videos.isEmpty) return const SizedBox.shrink();

    return SizedBox(
      height: 120, // Increased height to match images
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: videos.length,
        itemBuilder: (context, index) {
          return Container(
            width: 160, // Wider for better video thumbnail display
            margin: EdgeInsets.only(right: index < videos.length - 1 ? 6 : 0),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: InkWell(
                onTap: () => _launchUrl(videos[index]),
                child: Stack(
                  children: [
                    // Video thumbnail
                    _buildVideoThumbnail(videos[index]),

                    // Play button overlay
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.play_circle_filled,
                            color: Colors.white,
                            size: 32,
                          ),
                        ),
                      ),
                    ),

                    // Video duration badge (bottom right)
                    Positioned(
                      bottom: 4,
                      right: 4,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black.withValues(alpha: 0.7),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          '0:30',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLinkPreview() {
    final linkUrl = widget.topPost!.linkUrl;
    if (linkUrl == null || linkUrl.isEmpty) return const SizedBox.shrink();

    return InkWell(
      onTap: () => _launchUrl(linkUrl),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: Colors.white.withValues(alpha: 0.1),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            const Icon(Icons.link, color: Colors.white, size: 16),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                linkUrl.length > 40
                    ? '${linkUrl.substring(0, 40)}...'
                    : linkUrl,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  decoration: TextDecoration.underline,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const Icon(Icons.open_in_new, color: Colors.white, size: 14),
          ],
        ),
      ),
    );
  }

  Future<void> _launchUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error launching URL: $e');
    }
  }
}
