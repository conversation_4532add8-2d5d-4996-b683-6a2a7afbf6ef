import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_network/image_network.dart';
import '../controllers/user_cache_controller.dart';

class ReactiveProfileImage extends StatelessWidget {
  final String userId;
  final double radius;
  final double? height;
  final double? width;
  final Widget? fallbackIcon;
  final VoidCallback? onTap;
  final bool showBorder;
  final Color borderColor;
  final double borderWidth;

  const ReactiveProfileImage({
    super.key,
    required this.userId,
    this.radius = 20,
    this.height,
    this.width,
    this.fallbackIcon,
    this.onTap,
    this.showBorder = false,
    this.borderColor = Colors.white,
    this.borderWidth = 2.0,
  });

  @override
  Widget build(BuildContext context) {
    final userCacheController = UserCacheController.instance;

    return GestureDetector(
      onTap: onTap,
      child: Obx(() {
        // Get profile image URL reactively
        final profileImageUrl = userCacheController.profileImageCache[userId];

        return CircleAvatar(
          radius: radius,
          backgroundColor: Colors.grey.shade300,
          child: Container(
            decoration:
                showBorder
                    ? BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: borderColor,
                        width: borderWidth,
                      ),
                    )
                    : null,
            child: ClipOval(
              child:
                  profileImageUrl != null && profileImageUrl.isNotEmpty
                      ? _buildNetworkImage(profileImageUrl)
                      : _buildFallbackIcon(),
            ),
          ),
        );
      }),
    );
  }

  Widget _buildNetworkImage(String imageUrl) {
    if (kIsWeb) {
      return ImageNetwork(
        image: imageUrl,
        height: height ?? radius * 2,
        width: width ?? radius * 2,
        duration: 300,
        curve: Curves.easeIn,
        onPointer: true,
        debugPrint: false,
        backgroundColor: Colors.white,
        fitAndroidIos: BoxFit.cover,
        fitWeb: BoxFitWeb.cover,
        borderRadius: BorderRadius.circular(radius),
        onLoading: CircularProgressIndicator(
          color: Colors.blue.shade300,
          strokeWidth: 1.0,
        ),
        onError: _buildFallbackIcon(),
      );
    } else {
      return Image.network(
        imageUrl,
        height: height ?? radius * 2,
        width: width ?? radius * 2,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              color: Colors.blue.shade300,
              strokeWidth: 1.0,
              value:
                  loadingProgress.expectedTotalBytes != null
                      ? loadingProgress.cumulativeBytesLoaded /
                          loadingProgress.expectedTotalBytes!
                      : null,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) => _buildFallbackIcon(),
      );
    }
  }

  Widget _buildFallbackIcon() {
    return fallbackIcon ??
        Icon(Icons.person, size: radius * 1.2, color: Colors.grey.shade600);
  }
}

/// Reactive Profile Image with automatic loading
class ReactiveProfileImageLoader extends StatefulWidget {
  final String userId;
  final double radius;
  final double? height;
  final double? width;
  final Widget? fallbackIcon;
  final VoidCallback? onTap;
  final bool showBorder;
  final Color borderColor;
  final double borderWidth;

  const ReactiveProfileImageLoader({
    super.key,
    required this.userId,
    this.radius = 20,
    this.height,
    this.width,
    this.fallbackIcon,
    this.onTap,
    this.showBorder = false,
    this.borderColor = Colors.white,
    this.borderWidth = 2.0,
  });

  @override
  State<ReactiveProfileImageLoader> createState() =>
      _ReactiveProfileImageLoaderState();
}

class _ReactiveProfileImageLoaderState
    extends State<ReactiveProfileImageLoader> {
  final UserCacheController _userCacheController = UserCacheController.instance;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    if (!_userCacheController.profileImageCache.containsKey(widget.userId)) {
      setState(() => _isLoading = true);
      await _userCacheController.getProfileImageUrl(widget.userId);
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return CircleAvatar(
        radius: widget.radius,
        backgroundColor: Colors.grey.shade300,
        child: CircularProgressIndicator(
          color: Colors.blue.shade300,
          strokeWidth: 1.0,
        ),
      );
    }

    return ReactiveProfileImage(
      userId: widget.userId,
      radius: widget.radius,
      height: widget.height,
      width: widget.width,
      fallbackIcon: widget.fallbackIcon,
      onTap: widget.onTap,
      showBorder: widget.showBorder,
      borderColor: widget.borderColor,
      borderWidth: widget.borderWidth,
    );
  }
}
