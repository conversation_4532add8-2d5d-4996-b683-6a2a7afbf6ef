import 'dart:async';
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:money_mouthy_two/services/post_service.dart';

import 'home_app_bar.dart';
import 'home_tab_bar.dart';
import 'explore_tab.dart';
import 'following_tab.dart';
import 'category_data.dart';

/// Home Content Widget - Contains the main home screen logic without bottom navigation
class HomeContent extends StatefulWidget {
  final Function(int)? onCategoryChanged;
  final int? initialCategoryIndex;

  const HomeContent({
    super.key,
    this.onCategoryChanged,
    this.initialCategoryIndex,
  });

  @override
  State<HomeContent> createState() => _HomeContentState();
}

class _HomeContentState extends State<HomeContent>
    with TickerProviderStateMixin {
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  late TabController _tabController;
  int currentCategoryIndex = 0;
  StreamSubscription<DocumentSnapshot>? _categorySubscription;

  @override
  void initState() {
    super.initState();
    _initializeServices();
    // Use initial category index if provided, otherwise load from user data
    if (widget.initialCategoryIndex != null) {
      currentCategoryIndex = widget.initialCategoryIndex!;
    } else {
      _loadUserCategory();
    }
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void didUpdateWidget(HomeContent oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Update category if it changed from parent
    if (widget.initialCategoryIndex != null &&
        widget.initialCategoryIndex != oldWidget.initialCategoryIndex) {
      setState(() {
        currentCategoryIndex = widget.initialCategoryIndex!;
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _categorySubscription?.cancel();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    try {
      // WalletManager is already initialized in MainNavigationScreen
      // await WalletManager().initialize();
      await PostService().initialize();
      if (mounted) {
        setState(() {
          // Refresh UI after services are ready
        });
      }
    } catch (e) {
      debugPrint('Error initializing services: $e');
    }
  }

  Future<void> _loadUserCategory() async {
    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user != null) {
        final userDoc =
            await FirebaseFirestore.instance
                .collection('users')
                .doc(user.uid)
                .get();

        if (userDoc.exists) {
          final userData = userDoc.data();
          final savedCategory = userData?['selectedCategory'];

          if (savedCategory != null && mounted) {
            // Find the index of the saved category
            final categoryIndex = Categories.all.indexWhere(
              (cat) => cat.name == savedCategory,
            );

            if (categoryIndex != -1) {
              setState(() {
                currentCategoryIndex = categoryIndex;
              });
              debugPrint(
                'HomeContent: Loaded saved category "$savedCategory" at index $categoryIndex',
              );
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading user category: $e');
    }
  }

  void updateCategory(int categoryIndex) {
    if (mounted && categoryIndex != currentCategoryIndex) {
      setState(() {
        currentCategoryIndex = categoryIndex;
      });
      debugPrint('HomeContent: Category updated to index $categoryIndex');
    }
  }

  /// Reset category to Politics (used by main navigation)
  void resetToPolitics() {
    setState(() {
      final politicsIndex = Categories.all.indexWhere(
        (cat) => cat.name == 'Politics',
      );
      if (politicsIndex != -1) {
        currentCategoryIndex = politicsIndex;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      backgroundColor: Colors.white,
      appBar: HomeAppBar(scaffoldKey: _scaffoldKey),
      body: _buildMainContent(),
    );
  }

  Widget _buildMainContent() {
    return Column(
      children: [
        HomeTabBar(tabController: _tabController),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              ExploreTab(
                key: ValueKey(currentCategoryIndex),
                selectedCategory: Categories.all[currentCategoryIndex].name,
              ),
              const FollowingTab(),
            ],
          ),
        ),
      ],
    );
  }
}
