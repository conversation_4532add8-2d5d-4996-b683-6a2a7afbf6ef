import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/user_cache_controller.dart';
import '../widgets/reactive_profile_image.dart';
import '../widgets/post_card.dart';
import '../models/post.dart';

class ReactivePostCard extends StatelessWidget {
  final Post post;
  final VoidCallback? onTap;
  final bool showFullContent;

  const ReactivePostCard({
    super.key,
    required this.post,
    this.onTap,
    this.showFullContent = false,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<UserCacheController>(
      builder: (userCacheController) {
        return Obx(() {
          // Get user data reactively
          final userData = userCacheController.userCache[post.authorId];
          final displayName = userData?['name'] ?? userData?['username'] ?? 'Unknown User';
          final username = userData?['username'] ?? '';

          return Card(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: InkWell(
              onTap: onTap,
              borderRadius: BorderRadius.circular(12),
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Author info with reactive profile image
                    Row(
                      children: [
                        ReactiveProfileImageLoader(
                          userId: post.authorId,
                          radius: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                displayName,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              if (username.isNotEmpty)
                                Text(
                                  '@$username',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                    fontSize: 14,
                                  ),
                                ),
                            ],
                          ),
                        ),
                        if (post.price > 0)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.green.shade100,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              '\$${post.price.toStringAsFixed(2)}',
                              style: TextStyle(
                                color: Colors.green.shade700,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            ),
                          ),
                      ],
                    ),
                    
                    const SizedBox(height: 12),
                    
                    // Post content
                    Text(
                      showFullContent ? post.content : _truncateContent(post.content),
                      style: const TextStyle(fontSize: 16),
                    ),
                    
                    if (post.attachments.isNotEmpty) ...[
                      const SizedBox(height: 12),
                      // Show attachments (images, videos, etc.)
                      _buildAttachments(),
                    ],
                    
                    const SizedBox(height: 12),
                    
                    // Post metadata
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 16,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          _formatTimestamp(post.timestamp),
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                        const Spacer(),
                        if (post.category.isNotEmpty)
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.blue.shade100,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              post.category,
                              style: TextStyle(
                                color: Colors.blue.shade700,
                                fontSize: 10,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        });
      },
      init: UserCacheController.instance,
    );
  }

  Widget _buildAttachments() {
    // Simple attachment display - you can enhance this based on your needs
    return Container(
      height: 200,
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: const Center(
        child: Icon(
          Icons.attachment,
          color: Colors.grey,
          size: 48,
        ),
      ),
    );
  }

  String _truncateContent(String content) {
    const maxLength = 150;
    if (content.length <= maxLength) return content;
    return '${content.substring(0, maxLength)}...';
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Enhanced reactive post card that automatically loads user data
class ReactivePostCardLoader extends StatefulWidget {
  final Post post;
  final VoidCallback? onTap;
  final bool showFullContent;

  const ReactivePostCardLoader({
    super.key,
    required this.post,
    this.onTap,
    this.showFullContent = false,
  });

  @override
  State<ReactivePostCardLoader> createState() => _ReactivePostCardLoaderState();
}

class _ReactivePostCardLoaderState extends State<ReactivePostCardLoader> {
  final UserCacheController _userCacheController = UserCacheController.instance;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    if (!_userCacheController.userCache.containsKey(widget.post.authorId)) {
      setState(() => _isLoading = true);
      await _userCacheController.getUserData(widget.post.authorId);
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Container(
          height: 120,
          child: const Center(
            child: CircularProgressIndicator(),
          ),
        ),
      );
    }

    return ReactivePostCard(
      post: widget.post,
      onTap: widget.onTap,
      showFullContent: widget.showFullContent,
    );
  }
}
