import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'profile_update_service.dart';

class UserService {
  static final UserService _instance = UserService._internal();
  factory UserService() => _instance;
  UserService._internal() {
    // Listen to profile updates and clear cache accordingly
    _profileUpdateService.profileDataUpdateStream.listen((updateData) {
      final userId = updateData['userId'] as String?;
      if (userId != null) {
        clearUserCache(userId);
        debugPrint(
          'UserService: Cleared cache for user $userId due to profile update',
        );
      }
    });

    // Also listen to profile image updates specifically
    _profileUpdateService.profileImageUpdateStream.listen((userId) {
      clearUserCache(userId);
      debugPrint(
        'UserService: Cleared cache for user $userId due to profile image update',
      );
    });
  }

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final Map<String, Map<String, dynamic>> _userCache = {};
  final ProfileUpdateService _profileUpdateService = ProfileUpdateService();

  /// Get user data by user ID with caching
  Future<Map<String, dynamic>?> getUserData(String userId) async {
    if (userId.isEmpty) return null;

    // Check cache first
    if (_userCache.containsKey(userId)) {
      return _userCache[userId];
    }

    try {
      final doc = await _firestore.collection('users').doc(userId).get();
      if (doc.exists) {
        final userData = doc.data()!;
        userData['id'] = doc.id;

        // Cache the result
        _userCache[userId] = userData;
        return userData;
      }
    } catch (e) {
      debugPrint('Error fetching user data for $userId: $e');
    }

    return null;
  }

  /// Get user profile image URL
  Future<String?> getUserProfileImage(String userId) async {
    final userData = await getUserData(userId);
    return userData?['profileImageUrl'] ?? userData?['photoUrl'];
  }

  /// Get user display name
  Future<String> getUserDisplayName(String userId) async {
    final userData = await getUserData(userId);
    return userData?['username'] ?? userData?['name'] ?? 'Unknown User';
  }

  /// Clear cache for a specific user (useful when user updates profile)
  void clearUserCache(String userId) {
    _userCache.remove(userId);
  }

  /// Clear all cached user data
  void clearAllCache() {
    _userCache.clear();
  }

  /// Force refresh user data (clears cache and fetches fresh data)
  Future<Map<String, dynamic>?> forceRefreshUserData(String userId) async {
    clearUserCache(userId);
    return await getUserData(userId);
  }

  /// Get multiple users data efficiently
  Future<Map<String, Map<String, dynamic>>> getMultipleUsersData(
    List<String> userIds,
  ) async {
    final result = <String, Map<String, dynamic>>{};
    final uncachedIds = <String>[];

    // Check cache first
    for (final userId in userIds) {
      if (_userCache.containsKey(userId)) {
        result[userId] = _userCache[userId]!;
      } else {
        uncachedIds.add(userId);
      }
    }

    // Fetch uncached users
    if (uncachedIds.isNotEmpty) {
      try {
        final docs =
            await _firestore
                .collection('users')
                .where(FieldPath.documentId, whereIn: uncachedIds)
                .get();

        for (final doc in docs.docs) {
          if (doc.exists) {
            final userData = doc.data();
            userData['id'] = doc.id;

            // Cache and add to result
            _userCache[doc.id] = userData;
            result[doc.id] = userData;
          }
        }
      } catch (e) {
        debugPrint('Error fetching multiple users data: $e');
      }
    }

    return result;
  }
}
