import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Service to handle migration of existing users to add missing profileCompleted flag
class UserMigrationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Migrate all existing users to add profileCompleted flag
  /// This should be run once to fix existing user documents
  static Future<void> migrateExistingUsers() async {
    try {
      debugPrint('Starting user migration...');
      
      // Get all users from Firestore
      final QuerySnapshot usersSnapshot = await _firestore
          .collection('users')
          .get()
          .timeout(const Duration(minutes: 5));

      int totalUsers = usersSnapshot.docs.length;
      int updatedUsers = 0;
      int skippedUsers = 0;

      debugPrint('Found $totalUsers users to check');

      // Process users in batches to avoid overwhelming Firestore
      const int batchSize = 10;
      final List<DocumentSnapshot> docs = usersSnapshot.docs;

      for (int i = 0; i < docs.length; i += batchSize) {
        final int end = (i + batchSize < docs.length) ? i + batchSize : docs.length;
        final List<DocumentSnapshot> batch = docs.sublist(i, end);

        // Process batch
        final List<Future<void>> batchOperations = batch.map((doc) async {
          try {
            final userData = doc.data() as Map<String, dynamic>?;
            
            if (userData == null) {
              debugPrint('Skipping user ${doc.id} - no data');
              skippedUsers++;
              return;
            }

            // Check if user already has profileCompleted flag
            if (userData.containsKey('profileCompleted')) {
              debugPrint('Skipping user ${doc.id} - already has profileCompleted flag');
              skippedUsers++;
              return;
            }

            // Determine if profile should be considered complete
            bool shouldMarkComplete = _shouldMarkProfileComplete(userData);

            // Update the user document
            await doc.reference.set({
              'profileCompleted': shouldMarkComplete,
              'migrationDate': FieldValue.serverTimestamp(),
              'migrationVersion': '1.0',
            }, SetOptions(merge: true));

            debugPrint('Updated user ${doc.id} - profileCompleted: $shouldMarkComplete');
            updatedUsers++;

          } catch (e) {
            debugPrint('Error updating user ${doc.id}: $e');
          }
        }).toList();

        // Wait for batch to complete
        await Future.wait(batchOperations);
        
        // Add small delay between batches to be gentle on Firestore
        if (i + batchSize < docs.length) {
          await Future.delayed(const Duration(milliseconds: 500));
        }
      }

      debugPrint('Migration completed!');
      debugPrint('Total users: $totalUsers');
      debugPrint('Updated users: $updatedUsers');
      debugPrint('Skipped users: $skippedUsers');

    } catch (e) {
      debugPrint('Migration failed: $e');
      rethrow;
    }
  }

  /// Determine if a user profile should be marked as complete
  /// Based on having essential profile information
  static bool _shouldMarkProfileComplete(Map<String, dynamic> userData) {
    // Check for essential profile fields
    final hasName = userData['name']?.toString().trim().isNotEmpty ?? false;
    final hasUsername = userData['username']?.toString().trim().isNotEmpty ?? false;
    final hasEmail = userData['email']?.toString().trim().isNotEmpty ?? false;
    final isEmailVerified = userData['emailVerified'] ?? false;

    // A profile is considered complete if:
    // 1. Has email and it's verified
    // 2. Has either name or username (preferably both)
    bool isComplete = hasEmail && 
                     isEmailVerified && 
                     (hasName || hasUsername);

    debugPrint('User profile check - Email: $hasEmail, Verified: $isEmailVerified, Name: $hasName, Username: $hasUsername -> Complete: $isComplete');
    
    return isComplete;
  }

  /// Migrate a specific user by UID
  static Future<void> migrateSingleUser(String uid) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection('users')
          .doc(uid)
          .get();

      if (!doc.exists) {
        debugPrint('User $uid does not exist');
        return;
      }

      final userData = doc.data() as Map<String, dynamic>?;
      
      if (userData == null) {
        debugPrint('User $uid has no data');
        return;
      }

      // Check if user already has profileCompleted flag
      if (userData.containsKey('profileCompleted')) {
        debugPrint('User $uid already has profileCompleted flag');
        return;
      }

      // Determine if profile should be considered complete
      bool shouldMarkComplete = _shouldMarkProfileComplete(userData);

      // Update the user document
      await doc.reference.set({
        'profileCompleted': shouldMarkComplete,
        'migrationDate': FieldValue.serverTimestamp(),
        'migrationVersion': '1.0',
      }, SetOptions(merge: true));

      debugPrint('Updated user $uid - profileCompleted: $shouldMarkComplete');

    } catch (e) {
      debugPrint('Error migrating user $uid: $e');
      rethrow;
    }
  }

  /// Check migration status - how many users need migration
  static Future<Map<String, int>> checkMigrationStatus() async {
    try {
      final QuerySnapshot usersSnapshot = await _firestore
          .collection('users')
          .get();

      int totalUsers = usersSnapshot.docs.length;
      int migratedUsers = 0;
      int needsMigration = 0;

      for (final doc in usersSnapshot.docs) {
        final userData = doc.data() as Map<String, dynamic>?;
        
        if (userData?.containsKey('profileCompleted') ?? false) {
          migratedUsers++;
        } else {
          needsMigration++;
        }
      }

      return {
        'total': totalUsers,
        'migrated': migratedUsers,
        'needsMigration': needsMigration,
      };

    } catch (e) {
      debugPrint('Error checking migration status: $e');
      rethrow;
    }
  }

  /// Force update a user's profileCompleted status
  static Future<void> forceUpdateUserProfileStatus(String uid, bool isComplete) async {
    try {
      await _firestore
          .collection('users')
          .doc(uid)
          .set({
            'profileCompleted': isComplete,
            'manualUpdate': true,
            'manualUpdateDate': FieldValue.serverTimestamp(),
          }, SetOptions(merge: true));

      debugPrint('Force updated user $uid - profileCompleted: $isComplete');

    } catch (e) {
      debugPrint('Error force updating user $uid: $e');
      rethrow;
    }
  }
}
