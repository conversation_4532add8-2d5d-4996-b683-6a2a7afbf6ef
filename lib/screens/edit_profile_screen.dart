import 'dart:io';
import 'dart:typed_data';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_network/image_network.dart';

import '../services/category_preference_service.dart';
import '../services/profile_update_service.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({Key? key}) : super(key: key);

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  final User? _user = FirebaseAuth.instance.currentUser;
  final _nameController = TextEditingController();
  final _usernameController = TextEditingController();
  final _bioController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  XFile? _pickedProfileFile;
  XFile? _pickedCoverFile;
  String? _profileImageUrl;
  String? _coverImageUrl;
  String? _selectedCategory;
  bool _isSaving = false;
  bool _isLoading = true;
  Uint8List? _webProfileImage;
  Uint8List? _webCoverImage;

  final List<Map<String, dynamic>> _categories = [
    {'name': 'News', 'color': const Color(0xFF29CC76)},
    {'name': 'Politics', 'color': const Color(0xFF4C5DFF)},
    {'name': 'Sex', 'color': const Color(0xFFFF4081)},
    {'name': 'Entertainment', 'color': const Color(0xFFA06A00)},
    {'name': 'Sports', 'color': const Color(0xFFC43DFF)},
    {'name': 'Religion', 'color': const Color(0xFF000000)},
  ];

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _usernameController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  Future<void> _loadUserData() async {
    if (_user == null) return;
    try {
      final userDoc =
          await FirebaseFirestore.instance
              .collection('users')
              .doc(_user!.uid)
              .get();
      if (userDoc.exists) {
        final data = userDoc.data();
        if (mounted) {
          setState(() {
            _nameController.text = data?['name'] ?? _user?.displayName ?? '';
            _usernameController.text = data?['username'] ?? '';
            _bioController.text = data?['bio'] ?? '';
            _profileImageUrl = data?['profileImageUrl'];
            _coverImageUrl = data?['coverImageUrl'];
            _selectedCategory = data?['selectedCategory'] ?? 'News';
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load user data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickProfileImage() async {
    if (_isSaving) return;
    final pickedFile = await ImagePicker().pickImage(
      source: ImageSource.gallery,
      imageQuality: 80,
      maxWidth: 800,
    );

    if (pickedFile != null) {
      // File size check (5MB limit)
      final fileLength = await pickedFile.length();
      if (fileLength > 5 * 1024 * 1024) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Image is too large. Please select a file under 5MB.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      if (kIsWeb) {
        final bytes = await pickedFile.readAsBytes();
        setState(() {
          _pickedProfileFile = pickedFile;
          _webProfileImage = bytes;
        });
      } else {
        setState(() {
          _pickedProfileFile = pickedFile;
        });
      }
    }
  }

  Future<void> _pickCoverImage() async {
    if (_isSaving) return;
    final pickedFile = await ImagePicker().pickImage(
      source: ImageSource.gallery,
      imageQuality: 80,
      maxWidth: 1200,
    );

    if (pickedFile != null) {
      // File size check (5MB limit)
      final fileLength = await pickedFile.length();
      if (fileLength > 5 * 1024 * 1024) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text(
                'Image is too large. Please select a file under 5MB.',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      if (kIsWeb) {
        final bytes = await pickedFile.readAsBytes();
        setState(() {
          _pickedCoverFile = pickedFile;
          _webCoverImage = bytes;
        });
      } else {
        setState(() {
          _pickedCoverFile = pickedFile;
        });
      }
    }
  }

  Future<String?> _uploadProfileImage() async {
    if (_pickedProfileFile == null) return null;
    if (_user == null) {
      throw Exception('User is not logged in.');
    }

    // Use timestamp to ensure unique file names and avoid conflicts
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final storage = FirebaseStorage.instance;
    final ref = storage
        .ref()
        .child('user_uploads')
        .child(_user.uid)
        .child('profile_$timestamp.jpg');

    // Retry mechanism for image upload
    try {
      UploadTask uploadTask;

      if (kIsWeb) {
        if (_webProfileImage == null) {
          throw Exception("Image data is not available for web.");
        }
        uploadTask = ref.putData(_webProfileImage!);
      } else {
        uploadTask = ref.putFile(File(_pickedProfileFile!.path));
      }

      // Wait for upload to complete with timeout
      final snapshot = await uploadTask.timeout(const Duration(seconds: 120));

      // Verify upload was successful
      if (snapshot.state == TaskState.success) {
        // Get download URL with timeout
        final downloadUrl = await ref.getDownloadURL().timeout(
          const Duration(seconds: 30),
        );

        developer.log(
          'Image uploaded successfully: $downloadUrl',
          name: 'ImageUpload',
        );
        return downloadUrl;
      } else {
        throw Exception('Upload failed with state: ${snapshot.state}');
      }
    } on FirebaseException catch (e) {
      debugPrint('❌ Firebase error on attempt : ${e.code} - ${e.message}');
      debugPrint('🔍 Full error details: $e');
      developer.log(
        'Firebase error on attempt : ${e.code} - ${e.message}',
        name: 'ImageUpload',
        error: e,
      );

      // Handle specific Firebase errors
      if (e.code == 'object-not-found') {
        debugPrint('🚫 Object not found error during upload - retrying...');
        debugPrint('📁 Attempted path: ${ref.fullPath}');
      } else if (e.code == 'unauthorized') {
        debugPrint('🔐 Unauthorized error - check Firebase Storage rules');
        throw Exception(
          'Unauthorized to upload image. Please check permissions.',
        );
      } else if (e.code == 'canceled') {
        debugPrint('⏹️ Upload was canceled');
        throw Exception('Upload was canceled.');
      }
      await Future.delayed(Duration(seconds: 1));
    } catch (e) {
      debugPrint('General error on attempt : $e');
      throw Exception('Failed to upload image , error: $e');
    }
    return null;
  }

  Future<String?> _uploadCoverImage() async {
    if (_pickedCoverFile == null) return null;
    if (_user == null) {
      throw Exception('User is not logged in.');
    }

    // Use timestamp to ensure unique file names and avoid conflicts
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final storage = FirebaseStorage.instance;
    final ref = storage
        .ref()
        .child('user_uploads')
        .child(_user.uid)
        .child('cover_$timestamp.jpg');

    // Retry mechanism for image upload
    try {
      UploadTask uploadTask;

      if (kIsWeb) {
        if (_webCoverImage == null) {
          throw Exception("Image data is not available for web.");
        }
        uploadTask = ref.putData(_webCoverImage!);
      } else {
        uploadTask = ref.putFile(File(_pickedCoverFile!.path));
      }

      // Wait for upload to complete with timeout
      final snapshot = await uploadTask.timeout(const Duration(seconds: 120));

      // Verify upload was successful
      if (snapshot.state == TaskState.success) {
        // Get download URL with timeout
        final downloadUrl = await ref.getDownloadURL().timeout(
          const Duration(seconds: 30),
        );

        return downloadUrl;
      } else {
        throw Exception('Upload failed with state: ${snapshot.state}');
      }
    } catch (e) {
      debugPrint('General error on cover upload: $e');
      throw Exception('Failed to upload cover image, error: $e');
    }
    return null;
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate() || _isSaving) {
      return;
    }

    setState(() => _isSaving = true);

    try {
      // Upload images if selected
      String? newProfileImageUrl;
      String? newCoverImageUrl;

      if (_pickedProfileFile != null) {
        try {
          newProfileImageUrl = await _uploadProfileImage();
          debugPrint('Profile image upload completed: $newProfileImageUrl');
        } catch (imageError) {
          debugPrint('Profile image upload failed: $imageError');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Profile image upload failed: ${imageError.toString()}',
                ),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 5),
              ),
            );
          }
        }
      }

      if (_pickedCoverFile != null) {
        try {
          newCoverImageUrl = await _uploadCoverImage();
          debugPrint('Cover image upload completed: $newCoverImageUrl');
        } catch (imageError) {
          debugPrint('Cover image upload failed: $imageError');
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Cover image upload failed: ${imageError.toString()}',
                ),
                backgroundColor: Colors.orange,
                duration: const Duration(seconds: 5),
              ),
            );
          }
        }
      }

      final Map<String, dynamic> dataToUpdate = {
        'name': _nameController.text.trim(),
        'bio': _bioController.text.trim(),
        'selectedCategory': _selectedCategory,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Only update image URLs if uploads were successful
      if (newProfileImageUrl != null) {
        dataToUpdate['profileImageUrl'] = newProfileImageUrl;
      }
      if (newCoverImageUrl != null) {
        dataToUpdate['coverImageUrl'] = newCoverImageUrl;
      }

      // Save to Firestore with retry mechanism
      await _saveToFirestoreWithRetry(dataToUpdate);

      // Update Firebase Auth profile with retry mechanism
      await _updateAuthProfileWithRetry(newProfileImageUrl);

      // Also save category to SharedPreferences for faster access
      if (_selectedCategory != null) {
        await CategoryPreferenceService.saveLastSelectedCategory(
          _selectedCategory!,
        );
      }

      // Notify other parts of the app about profile updates
      if (_user != null) {
        final updatedData = <String, dynamic>{
          'name': _nameController.text.trim(),
          'bio': _bioController.text.trim(),
          'selectedCategory': _selectedCategory,
        };

        if (newProfileImageUrl != null) {
          updatedData['profileImageUrl'] = newProfileImageUrl;
          // Notify specifically about profile image update
          ProfileUpdateService().notifyProfileImageUpdate(
            _user!.uid,
            newProfileImageUrl,
          );
        }

        if (newCoverImageUrl != null) {
          updatedData['coverImageUrl'] = newCoverImageUrl;
        }

        // Notify about general profile data update
        ProfileUpdateService().notifyProfileDataUpdate(_user!.uid, updatedData);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Profile updated successfully!'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Failed to save profile';
        if (e.toString().contains('timeout')) {
          errorMessage =
              'Request timed out. Please check your internet connection and try again.';
        } else if (e.toString().contains('network')) {
          errorMessage =
              'Network error. Please check your internet connection.';
        } else {
          errorMessage = 'Failed to save profile: ${e.toString()}';
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
            duration: const Duration(seconds: 5),
            action: SnackBarAction(
              label: 'Retry',
              textColor: Colors.white,
              onPressed: _saveProfile,
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          'Edit Profile',
          style: TextStyle(color: Colors.black, fontWeight: FontWeight.w600),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: BoxConstraints(
              maxWidth: kIsWeb ? 800 : double.infinity,
            ),
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(height: 20),

                    // Profile Avatar
                    _buildAvatar(),
                    const SizedBox(height: 12),
                    Text(
                      'Tap the image to change your avatar',
                      style: TextStyle(color: Colors.grey[600], fontSize: 14),
                    ),
                    const SizedBox(height: 32),

                    // Name Field
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: TextFormField(
                        controller: _nameController,
                        decoration: const InputDecoration(
                          labelText: 'Display Name',
                          hintText: 'Enter your display name',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                          prefixIcon: Icon(
                            Icons.person_outline,
                            color: Color(0xFF5159FF),
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Display name cannot be empty';
                          }
                          if (value.length < 2) {
                            return 'Display name must be at least 2 characters long';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Username Field (Read-only)
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: TextFormField(
                        controller: _usernameController,
                        enabled: false,
                        decoration: const InputDecoration(
                          labelText: 'Username',
                          hintText: 'Your unique username',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                          prefixIcon: Icon(
                            Icons.alternate_email,
                            color: Colors.grey,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Username cannot be changed',
                      style: TextStyle(color: Colors.grey[600], fontSize: 12),
                    ),
                    const SizedBox(height: 16),
                    // Bio Field
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: TextFormField(
                        controller: _bioController,
                        decoration: const InputDecoration(
                          labelText: 'Bio',
                          hintText: 'Tell us about yourself...',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 16,
                          ),
                          prefixIcon: Icon(
                            Icons.info_outline,
                            color: Color(0xFF5159FF),
                          ),
                        ),
                        maxLines: 3,
                        maxLength: 150,
                        validator: (value) {
                          if (value != null && value.length > 150) {
                            return 'Bio must be 150 characters or less';
                          }
                          return null;
                        },
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Category Selection
                    _buildCategorySelection(),
                    const SizedBox(height: 32),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        minimumSize: const Size(double.infinity, 50),
                        textStyle: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      onPressed: _isSaving ? null : _saveProfile,
                      child:
                          _isSaving
                              ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  color: Colors.white,
                                  strokeWidth: 2.0,
                                ),
                              )
                              : const Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(Icons.save, size: 20),
                                  SizedBox(width: 10),
                                  Text('Save Profile'),
                                ],
                              ),
                    ),
                    const SizedBox(height: 32),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCategorySelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Main Category',
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 8),
        Text(
          'Choose your main category for posting content',
          style: TextStyle(color: Colors.grey[600], fontSize: 14),
        ),
        const SizedBox(height: 16),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children:
              _categories.map((category) {
                final isSelected = _selectedCategory == category['name'];
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedCategory = category['name'];
                    });
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      color:
                          isSelected ? category['color'] : Colors.grey.shade100,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color:
                            isSelected
                                ? category['color']
                                : Colors.grey.shade300,
                        width: 2,
                      ),
                    ),
                    child: Text(
                      category['name'],
                      style: TextStyle(
                        color: isSelected ? Colors.white : Colors.black,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildAvatar() {
    return GestureDetector(
      onTap: _pickProfileImage,
      child: Stack(
        children: [
          CircleAvatar(
            radius: 60,
            backgroundColor: Colors.grey.shade300,
            child: ClipOval(child: _buildProfileImageWidget()),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(6),
              decoration: const BoxDecoration(
                color: Colors.blue,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.camera_alt,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  ImageProvider? _displayImage() {
    if (_pickedProfileFile != null) {
      if (kIsWeb && _webProfileImage != null) {
        return MemoryImage(_webProfileImage!);
      } else if (!kIsWeb) {
        return FileImage(File(_pickedProfileFile!.path));
      }
    }

    // Return existing profile image from Firebase if available
    if (_profileImageUrl != null && _profileImageUrl!.isNotEmpty) {
      return NetworkImage(_profileImageUrl!);
    }

    return null;
  }

  Widget _buildProfileImageWidget() {
    // Show loading indicator while data is being loaded
    if (_isLoading) {
      return const CircularProgressIndicator();
    }

    // Handle newly picked images first
    if (_displayImage() != null) {
      return Image(
        image: _displayImage()!,
        height: 120,
        width: 120,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return const Icon(Icons.person, color: Colors.blue, size: 60);
        },
      );
    }

    // Handle existing Firebase profile image
    if (_profileImageUrl != null && _profileImageUrl!.isNotEmpty) {
      if (kIsWeb) {
        return ImageNetwork(
          image: _profileImageUrl!,
          height: 120,
          width: 120,
          duration: 500,
          curve: Curves.easeIn,
          onPointer: true,
          debugPrint: false,
          backgroundColor: Colors.white,
          fitAndroidIos: BoxFit.cover,
          fitWeb: BoxFitWeb.cover,
          borderRadius: BorderRadius.circular(70),
          onLoading: const CircularProgressIndicator(
            color: Colors.indigoAccent,
            strokeWidth: 0.1,
          ),
          onError: const Icon(Icons.person, color: Colors.blue, size: 60),
        );
      } else {
        return Image.network(
          _profileImageUrl!,
          height: 120,
          width: 120,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            return const Icon(Icons.person, color: Colors.blue, size: 60);
          },
        );
      }
    }

    // Fallback icon when no image is available
    return const Icon(Icons.person, color: Colors.blue, size: 60);
  }

  Future<void> _saveToFirestoreWithRetry(
    Map<String, dynamic> dataToUpdate,
  ) async {
    for (int attempt = 1; attempt <= 3; attempt++) {
      try {
        await FirebaseFirestore.instance
            .collection('users')
            .doc(_user!.uid)
            .set(dataToUpdate, SetOptions(merge: true))
            .timeout(const Duration(seconds: 60));
        return; // Success, exit retry loop
      } catch (e) {
        if (attempt == 3) {
          throw Exception('Failed to save to Firestore after 3 attempts: $e');
        }
        // Wait before retrying
        await Future.delayed(Duration(seconds: attempt * 2));
      }
    }
  }

  Future<void> _updateAuthProfileWithRetry(String? newImageUrl) async {
    for (int attempt = 1; attempt <= 3; attempt++) {
      try {
        await _user!
            .updateDisplayName(_nameController.text.trim())
            .timeout(const Duration(seconds: 30));

        if (newImageUrl != null) {
          await _user!
              .updatePhotoURL(newImageUrl)
              .timeout(const Duration(seconds: 30));
        }
        return; // Success, exit retry loop
      } catch (e) {
        if (attempt == 3) {
          throw Exception('Failed to update auth profile after 3 attempts: $e');
        }

        // Wait before retrying
        await Future.delayed(Duration(seconds: attempt * 2));
      }
    }
  }
}
